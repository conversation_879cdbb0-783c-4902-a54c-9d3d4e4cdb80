<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>スタイリッシュ テトリス</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        
        .game-container {
            display: flex;
            gap: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .game-board {
            position: relative;
            background: linear-gradient(145deg, #1a1a2e, #16213e);
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        #tetris-canvas {
            border: 2px solid #00f5ff;
            border-radius: 10px;
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
            background: #0a0a0a;
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
            min-width: 200px;
        }
        
        .info-panel {
            background: linear-gradient(145deg, #2d1b69, #11998e);
            border-radius: 15px;
            padding: 20px;
            color: white;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .score {
            font-size: 24px;
            font-weight: 900;
            margin-bottom: 10px;
            text-shadow: 0 0 10px rgba(0, 245, 255, 0.8);
        }
        
        .level, .lines {
            font-size: 14px;
            margin: 5px 0;
            opacity: 0.9;
        }
        
        .next-piece {
            background: linear-gradient(145deg, #1a1a2e, #16213e);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            color: white;
        }
        
        #next-canvas {
            border: 1px solid #00f5ff;
            border-radius: 8px;
            margin-top: 10px;
            box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
        }
        
        .controls {
            background: linear-gradient(145deg, #667eea, #764ba2);
            border-radius: 15px;
            padding: 20px;
            color: white;
            font-size: 12px;
            line-height: 1.6;
        }
        
        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(145deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: 700;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            animation: gameOverPulse 2s infinite;
        }
        
        @keyframes gameOverPulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.05); }
        }
        
        .start-button {
            background: linear-gradient(145deg, #00f5ff, #0099cc);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            cursor: pointer;
            margin-top: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 153, 204, 0.4);
        }
        
        .start-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 153, 204, 0.6);
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00f5ff;
            border-radius: 50%;
            pointer-events: none;
            animation: particle 1s ease-out forwards;
        }
        
        @keyframes particle {
            0% {
                opacity: 1;
                transform: scale(1);
            }
            100% {
                opacity: 0;
                transform: scale(0) translateY(-50px);
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-board">
            <canvas id="tetris-canvas" width="300" height="600"></canvas>
            <div class="game-over" id="game-over">
                <div>GAME OVER</div>
                <button class="start-button" onclick="startGame()">RESTART</button>
            </div>
        </div>
        
        <div class="sidebar">
            <div class="info-panel">
                <div class="score">スコア: <span id="score">0</span></div>
                <div class="level">レベル: <span id="level">1</span></div>
                <div class="lines">ライン: <span id="lines">0</span></div>
                <button class="start-button" onclick="startGame()">START</button>
            </div>
            
            <div class="next-piece">
                <div>NEXT</div>
                <canvas id="next-canvas" width="120" height="120"></canvas>
            </div>
            
            <div class="controls">
                <strong>操作方法:</strong><br>
                ← → : 移動<br>
                ↓ : 高速落下<br>
                ↑ : 回転<br>
                スペース: 一瞬で落下
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('tetris-canvas');
        const ctx = canvas.getContext('2d');
        const nextCanvas = document.getElementById('next-canvas');
        const nextCtx = nextCanvas.getContext('2d');
        
        const BOARD_WIDTH = 10;
        const BOARD_HEIGHT = 20;
        const BLOCK_SIZE = 30;
        
        let board = [];
        let currentPiece = null;
        let nextPiece = null;
        let score = 0;
        let level = 1;
        let lines = 0;
        let dropTime = 0;
        let dropInterval = 1000;
        let lastTime = 0;
        let gameRunning = false;
        
        // テトリミノの定義（7種類）
        const pieces = [
            {
                shape: [
                    [1, 1, 1, 1]
                ],
                color: '#00f5ff'
            },
            {
                shape: [
                    [1, 1],
                    [1, 1]
                ],
                color: '#ffff00'
            },
            {
                shape: [
                    [0, 1, 0],
                    [1, 1, 1]
                ],
                color: '#800080'
            },
            {
                shape: [
                    [0, 1, 1],
                    [1, 1, 0]
                ],
                color: '#00ff00'
            },
            {
                shape: [
                    [1, 1, 0],
                    [0, 1, 1]
                ],
                color: '#ff0000'
            },
            {
                shape: [
                    [1, 0, 0],
                    [1, 1, 1]
                ],
                color: '#ffa500'
            },
            {
                shape: [
                    [0, 0, 1],
                    [1, 1, 1]
                ],
                color: '#0000ff'
            }
        ];
        
        function initBoard() {
            board = Array(BOARD_HEIGHT).fill().map(() => Array(BOARD_WIDTH).fill(0));
        }
        
        function createPiece() {
            const piece = pieces[Math.floor(Math.random() * pieces.length)];
            return {
                shape: piece.shape,
                color: piece.color,
                x: Math.floor(BOARD_WIDTH / 2) - Math.floor(piece.shape[0].length / 2),
                y: 0
            };
        }
        
        function drawBlock(ctx, x, y, color, size = BLOCK_SIZE) {
            const gradient = ctx.createLinearGradient(x, y, x + size, y + size);
            gradient.addColorStop(0, color);
            gradient.addColorStop(1, darkenColor(color, 0.3));
            
            ctx.fillStyle = gradient;
            ctx.fillRect(x, y, size, size);
            
            // ハイライト効果
            ctx.fillStyle = lightenColor(color, 0.3);
            ctx.fillRect(x, y, size, 3);
            ctx.fillRect(x, y, 3, size);
            
            // 境界線
            ctx.strokeStyle = darkenColor(color, 0.5);
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, size, size);
        }
        
        function darkenColor(color, factor) {
            const hex = color.substring(1);
            const r = Math.max(0, parseInt(hex.substring(0, 2), 16) * (1 - factor));
            const g = Math.max(0, parseInt(hex.substring(2, 4), 16) * (1 - factor));
            const b = Math.max(0, parseInt(hex.substring(4, 6), 16) * (1 - factor));
            return `rgb(${Math.floor(r)}, ${Math.floor(g)}, ${Math.floor(b)})`;
        }
        
        function lightenColor(color, factor) {
            const hex = color.substring(1);
            const r = Math.min(255, parseInt(hex.substring(0, 2), 16) * (1 + factor));
            const g = Math.min(255, parseInt(hex.substring(2, 4), 16) * (1 + factor));
            const b = Math.min(255, parseInt(hex.substring(4, 6), 16) * (1 + factor));
            return `rgb(${Math.floor(r)}, ${Math.floor(g)}, ${Math.floor(b)})`;
        }
        
        function drawBoard() {
            ctx.fillStyle = '#0a0a0a';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // グリッドの描画
            ctx.strokeStyle = 'rgba(0, 245, 255, 0.1)';
            ctx.lineWidth = 1;
            for (let x = 0; x <= BOARD_WIDTH; x++) {
                ctx.beginPath();
                ctx.moveTo(x * BLOCK_SIZE, 0);
                ctx.lineTo(x * BLOCK_SIZE, canvas.height);
                ctx.stroke();
            }
            for (let y = 0; y <= BOARD_HEIGHT; y++) {
                ctx.beginPath();
                ctx.moveTo(0, y * BLOCK_SIZE);
                ctx.lineTo(canvas.width, y * BLOCK_SIZE);
                ctx.stroke();
            }
            
            // 固定されたブロックの描画
            for (let y = 0; y < BOARD_HEIGHT; y++) {
                for (let x = 0; x < BOARD_WIDTH; x++) {
                    if (board[y][x]) {
                        drawBlock(ctx, x * BLOCK_SIZE, y * BLOCK_SIZE, board[y][x]);
                    }
                }
            }
        }
        
        function drawPiece(piece, targetCtx = ctx, offsetX = 0, offsetY = 0, blockSize = BLOCK_SIZE) {
            if (!piece) return;
            
            for (let y = 0; y < piece.shape.length; y++) {
                for (let x = 0; x < piece.shape[y].length; x++) {
                    if (piece.shape[y][x]) {
                        drawBlock(
                            targetCtx,
                            (piece.x + x) * blockSize + offsetX,
                            (piece.y + y) * blockSize + offsetY,
                            piece.color,
                            blockSize
                        );
                    }
                }
            }
        }
        
        function drawNextPiece() {
            nextCtx.fillStyle = '#1a1a2e';
            nextCtx.fillRect(0, 0, nextCanvas.width, nextCanvas.height);
            
            if (nextPiece) {
                const offsetX = (nextCanvas.width - nextPiece.shape[0].length * 20) / 2;
                const offsetY = (nextCanvas.height - nextPiece.shape.length * 20) / 2;
                
                for (let y = 0; y < nextPiece.shape.length; y++) {
                    for (let x = 0; x < nextPiece.shape[y].length; x++) {
                        if (nextPiece.shape[y][x]) {
                            drawBlock(
                                nextCtx,
                                x * 20 + offsetX,
                                y * 20 + offsetY,
                                nextPiece.color,
                                20
                            );
                        }
                    }
                }
            }
        }
        
        function isValidMove(piece, dx = 0, dy = 0, newShape = null) {
            const shape = newShape || piece.shape;
            const newX = piece.x + dx;
            const newY = piece.y + dy;
            
            for (let y = 0; y < shape.length; y++) {
                for (let x = 0; x < shape[y].length; x++) {
                    if (shape[y][x]) {
                        const boardX = newX + x;
                        const boardY = newY + y;
                        
                        if (boardX < 0 || boardX >= BOARD_WIDTH || 
                            boardY >= BOARD_HEIGHT || 
                            (boardY >= 0 && board[boardY][boardX])) {
                            return false;
                        }
                    }
                }
            }
            return true;
        }
        
        function rotatePiece(piece) {
            const rotated = piece.shape[0].map((_, index) =>
                piece.shape.map(row => row[index]).reverse()
            );
            
            if (isValidMove(piece, 0, 0, rotated)) {
                piece.shape = rotated;
                createParticles(piece.x * BLOCK_SIZE + 60, piece.y * BLOCK_SIZE + 60);
            }
        }
        
        function placePiece(piece) {
            for (let y = 0; y < piece.shape.length; y++) {
                for (let x = 0; x < piece.shape[y].length; x++) {
                    if (piece.shape[y][x]) {
                        board[piece.y + y][piece.x + x] = piece.color;
                    }
                }
            }
        }
        
        function clearLines() {
            let linesCleared = 0;
            
            for (let y = BOARD_HEIGHT - 1; y >= 0; y--) {
                if (board[y].every(cell => cell !== 0)) {
                    // ライン消去エフェクト
                    for (let x = 0; x < BOARD_WIDTH; x++) {
                        createParticles(x * BLOCK_SIZE + 15, y * BLOCK_SIZE + 15);
                    }
                    
                    board.splice(y, 1);
                    board.unshift(Array(BOARD_WIDTH).fill(0));
                    linesCleared++;
                    y++; // 同じ行を再チェック
                }
            }
            
            if (linesCleared > 0) {
                lines += linesCleared;
                score += linesCleared * 100 * level;
                level = Math.floor(lines / 10) + 1;
                dropInterval = Math.max(50, 1000 - (level - 1) * 100);
                
                updateDisplay();
            }
        }
        
        function createParticles(x, y) {
            for (let i = 0; i < 8; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = x + 'px';
                particle.style.top = y + 'px';
                particle.style.transform = `translate(${Math.random() * 40 - 20}px, ${Math.random() * 40 - 20}px)`;
                document.body.appendChild(particle);
                
                setTimeout(() => {
                    particle.remove();
                }, 1000);
            }
        }
        
        function updateDisplay() {
            document.getElementById('score').textContent = score;
            document.getElementById('level').textContent = level;
            document.getElementById('lines').textContent = lines;
        }
        
        function gameOver() {
            gameRunning = false;
            document.getElementById('game-over').style.display = 'block';
        }
        
        function startGame() {
            initBoard();
            currentPiece = createPiece();
            nextPiece = createPiece();
            score = 0;
            level = 1;
            lines = 0;
            dropInterval = 1000;
            gameRunning = true;
            
            document.getElementById('game-over').style.display = 'none';
            updateDisplay();
            gameLoop();
        }
        
        function gameLoop(time = 0) {
            if (!gameRunning) return;
            
            const deltaTime = time - lastTime;
            lastTime = time;
            dropTime += deltaTime;
            
            if (dropTime > dropInterval) {
                if (isValidMove(currentPiece, 0, 1)) {
                    currentPiece.y++;
                } else {
                    placePiece(currentPiece);
                    clearLines();
                    
                    currentPiece = nextPiece;
                    nextPiece = createPiece();
                    
                    if (!isValidMove(currentPiece)) {
                        gameOver();
                        return;
                    }
                }
                dropTime = 0;
            }
            
            drawBoard();
            drawPiece(currentPiece);
            drawNextPiece();
            
            requestAnimationFrame(gameLoop);
        }
        
        // キーボードコントロール
        document.addEventListener('keydown', (e) => {
            if (!gameRunning || !currentPiece) return;
            
            switch (e.key) {
                case 'ArrowLeft':
                    if (isValidMove(currentPiece, -1, 0)) {
                        currentPiece.x--;
                    }
                    break;
                case 'ArrowRight':
                    if (isValidMove(currentPiece, 1, 0)) {
                        currentPiece.x++;
                    }
                    break;
                case 'ArrowDown':
                    if (isValidMove(currentPiece, 0, 1)) {
                        currentPiece.y++;
                        score += 1;
                        updateDisplay();
                    }
                    break;
                case 'ArrowUp':
                    rotatePiece(currentPiece);
                    break;
                case ' ':
                    while (isValidMove(currentPiece, 0, 1)) {
                        currentPiece.y++;
                        score += 2;
                    }
                    updateDisplay();
                    break;
            }
            e.preventDefault();
        });
        
        // 初期化
        initBoard();
        drawBoard();
        drawNextPiece();
        updateDisplay();
    </script>
</body>
</html>