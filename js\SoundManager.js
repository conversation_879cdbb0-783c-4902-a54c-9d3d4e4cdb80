/**
 * SoundManager.js - Audio management system for Tetris game
 * Handles background music, sound effects, volume control, and mute functionality
 */

class SoundManager {
    constructor() {
        this.bgm = null;
        this.volume = 0.75; // Default volume at 75% (70-80% range)
        this.isMuted = false;
        this.isInitialized = false;
        this.audioContext = null;
        
        // Audio file paths
        this.audioFiles = {
            bgm: './assets/audio/FIRST STEP TOWARDS WARS.mp3'
        };
        
        // Initialize audio context for better browser compatibility
        this.initializeAudioContext();
    }
    
    /**
     * Initialize Web Audio API context for better browser compatibility
     */
    initializeAudioContext() {
        try {
            window.AudioContext = window.AudioContext || window.webkitAudioContext;
            this.audioContext = new AudioContext();
        } catch (error) {
            console.warn('Web Audio API not supported, falling back to HTML5 audio:', error);
        }
    }
    
    /**
     * Initialize the sound system
     * Must be called after user interaction due to browser autoplay policies
     */
    async initialize() {
        if (this.isInitialized) return;
        
        try {
            // Resume audio context if suspended (required by some browsers)
            if (this.audioContext && this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }
            
            // Initialize background music
            await this.initializeBGM();
            
            this.isInitialized = true;
            console.log('SoundManager initialized successfully');
        } catch (error) {
            console.error('Failed to initialize SoundManager:', error);
        }
    }
    
    /**
     * Initialize background music
     */
    async initializeBGM() {
        return new Promise((resolve, reject) => {
            this.bgm = new Audio(this.audioFiles.bgm);
            this.bgm.loop = true;
            this.bgm.volume = this.isMuted ? 0 : this.volume;
            this.bgm.preload = 'auto';
            
            // Handle loading events
            this.bgm.addEventListener('canplaythrough', () => {
                console.log('BGM loaded successfully');
                resolve();
            });
            
            this.bgm.addEventListener('error', (error) => {
                console.error('Failed to load BGM:', error);
                reject(error);
            });
            
            // Start loading the audio
            this.bgm.load();
        });
    }
    
    /**
     * Start playing background music
     */
    async playBGM() {
        if (!this.isInitialized) {
            await this.initialize();
        }
        
        if (this.bgm && !this.isMuted) {
            try {
                this.bgm.currentTime = 0; // Start from beginning
                await this.bgm.play();
                console.log('BGM started playing');
            } catch (error) {
                console.error('Failed to play BGM:', error);
                // Handle autoplay restrictions
                if (error.name === 'NotAllowedError') {
                    console.warn('Autoplay blocked. BGM will start after user interaction.');
                }
            }
        }
    }
    
    /**
     * Stop background music
     */
    stopBGM() {
        if (this.bgm) {
            this.bgm.pause();
            this.bgm.currentTime = 0;
            console.log('BGM stopped');
        }
    }
    
    /**
     * Pause background music
     */
    pauseBGM() {
        if (this.bgm) {
            this.bgm.pause();
            console.log('BGM paused');
        }
    }
    
    /**
     * Resume background music
     */
    async resumeBGM() {
        if (this.bgm && !this.isMuted) {
            try {
                await this.bgm.play();
                console.log('BGM resumed');
            } catch (error) {
                console.error('Failed to resume BGM:', error);
            }
        }
    }
    
    /**
     * Set volume level (0.0 to 1.0)
     * @param {number} volume - Volume level between 0 and 1
     */
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        
        if (this.bgm && !this.isMuted) {
            this.bgm.volume = this.volume;
        }
        
        console.log(`Volume set to: ${Math.round(this.volume * 100)}%`);
    }
    
    /**
     * Get current volume level
     * @returns {number} Current volume (0.0 to 1.0)
     */
    getVolume() {
        return this.volume;
    }
    
    /**
     * Mute all audio
     */
    mute() {
        this.isMuted = true;
        
        if (this.bgm) {
            this.bgm.volume = 0;
        }
        
        console.log('Audio muted');
    }
    
    /**
     * Unmute all audio
     */
    unmute() {
        this.isMuted = false;
        
        if (this.bgm) {
            this.bgm.volume = this.volume;
        }
        
        console.log('Audio unmuted');
    }
    
    /**
     * Toggle mute state
     */
    toggleMute() {
        if (this.isMuted) {
            this.unmute();
        } else {
            this.mute();
        }
        return this.isMuted;
    }
    
    /**
     * Check if audio is currently muted
     * @returns {boolean} True if muted
     */
    isMutedState() {
        return this.isMuted;
    }
    
    /**
     * Check if BGM is currently playing
     * @returns {boolean} True if playing
     */
    isBGMPlaying() {
        return this.bgm && !this.bgm.paused && !this.bgm.ended;
    }
    
    /**
     * Get current BGM playback time
     * @returns {number} Current time in seconds
     */
    getBGMCurrentTime() {
        return this.bgm ? this.bgm.currentTime : 0;
    }
    
    /**
     * Get BGM duration
     * @returns {number} Duration in seconds
     */
    getBGMDuration() {
        return this.bgm ? this.bgm.duration : 0;
    }
    
    /**
     * Clean up resources
     */
    destroy() {
        if (this.bgm) {
            this.bgm.pause();
            this.bgm.src = '';
            this.bgm = null;
        }
        
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
        
        this.isInitialized = false;
        console.log('SoundManager destroyed');
    }
}

// Export for use in other scripts
window.SoundManager = SoundManager;
